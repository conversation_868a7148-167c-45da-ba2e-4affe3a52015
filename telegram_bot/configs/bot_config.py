"""
Bot configuration models and utilities.
"""

import os
from enum import Enum
from functools import cached_property
from typing import Any, Literal, Self

import utils_general
from pydantic import BaseModel, ConfigDict, Field, model_validator
from pydantic_settings import BaseSettings

from telegram_bot.configs.exchange_config import (
    get_all_supported_currencies,
    get_currency_exchange_mapping,
)
from telegram_bot.constants import (
    CURRENCY_TO_MAX_PRICING_CUTOFF,
    CURRENCY_TO_TENOR_TARGETS,
    DEFAULT_EXCHANGE,
    DEFAULT_MAX_PRICING_CUTOFF,
    DEFAULT_TENOR_TARGETS,
    OKX_SUPPORTED_PRICE_CURRENCIES,
    OKX_SUPPORTED_VOL_CHART_CURRENCIES,
)
from telegram_bot.typings import (
    AllowedConstantTenors,
    AllowedCurrencies,
    CurrencyToExchange,
    ExchangeToCurrencies,
    Stage,
    WhitelistedChat,
    WhitelistedUser,
)


class Config(BaseSettings):
    """
    Base configuration for the application.
    """

    LOG_LEVEL: str = "INFO"
    BLOCKSCHOLES_INTERNAL_API_KEY: str = "option_pricer_api_key"
    TIMESERIES_DATA_VERSION: str = ""
    LOCAL_RUN: bool = True
    STAGE: Stage = "staging"


class BotNames(str, Enum):
    BLOCKSCHOLES = "blockscholes_bot"
    OKX = "okx_bot"
    PREMIUM = "premium_bot"
    LOCAL1 = "local_bot1"
    LOCAL2 = "local_bot2"


class Botfig(BaseModel):
    """
    Bot-specific configuration.
    """

    model_config = ConfigDict(
        extra="allow",
    )
    funding_rate_config: ExchangeToCurrencies
    spot_config: ExchangeToCurrencies
    is_premium_bot: bool = False
    internal_api_key: str = ""
    timeseries_version: str = ""
    default_exchange: str = DEFAULT_EXCHANGE
    whitelisted_users: list[WhitelistedUser] = Field(default_factory=list)
    whitelisted_chats: list[WhitelistedChat] = Field(default_factory=list)
    currency_to_exchange_mapping: CurrencyToExchange = Field(
        default_factory=dict
    )
    supported_vol_chart_currencies: list[AllowedCurrencies] = Field(
        default_factory=list
    )
    supported_pricing_currencies: list[AllowedCurrencies] = Field(
        default_factory=list
    )

    @model_validator(mode="after")
    def validate_whitelisted_users(self) -> Self:
        """
        Ensures that `whitelisted_users` is empty if `is_premium_bot` is False.
        """
        if self.is_premium_bot and self.whitelisted_users:
            raise ValueError("Cannot have whitelisted users on a premium bot")
        return self

    def get_exchange_for_currency(self, currency: AllowedCurrencies) -> str:
        """
        Returns the exchange to use for a given currency.
        If the currency is not in the mapping, returns the default exchange.
        """
        return self.currency_to_exchange_mapping.get(
            currency, self.default_exchange
        )

    def get_currency_tenor_target(
        self, currency: str
    ) -> list[AllowedConstantTenors]:
        """
        Returns the allowed tenor targets for a given currency.
        """
        return CURRENCY_TO_TENOR_TARGETS.get(currency, DEFAULT_TENOR_TARGETS)

    def get_max_tenor_cutoff(self, currency: str) -> int:
        """
        Returns the maximum tenor cutoff in days for a given currency.
        If the currency is not in the mapping, returns the default cutoff.

        Args:
            currency: The currency to get the max tenor cutoff for

        Returns:
            The maximum tenor cutoff in days
        """
        return CURRENCY_TO_MAX_PRICING_CUTOFF.get(
            currency, DEFAULT_MAX_PRICING_CUTOFF
        )

    @cached_property
    def display_allowed_constant_tenors(self) -> str:
        """
        Returns a formatted string of supported chart currencies and their allowed tenor targets.
        """
        lines = []
        for currency in self.supported_vol_chart_currencies:
            tenors = self.get_currency_tenor_target(currency)
            if tenors:
                lines.append(f"{currency}: {', '.join(tenors)}")

        return "\n".join(lines)

    @cached_property
    def get_user_whitelist(self) -> list[int | str]:
        """
        Retrieves all whitelisted user IDs and usernames.

        Returns:
            List[int | str]: A list of whitelisted user IDs and usernames.
        """
        return [
            *[user.id for user in self.whitelisted_users if user.id],
            *[
                user.username
                for user in self.whitelisted_users
                if user.username
            ],
        ]

    @cached_property
    def get_chat_whitelist(self) -> list[int]:
        """
        Retrieves all whitelisted chat IDs.

        Returns:
            List[int]: A list of whitelisted chat IDs.
        """
        return [
            *[chat.id for chat in self.whitelisted_chats if chat.id],
        ]

    # todo: this is a hack untill we formally move v2lya -> derive.
    def get_supported_exchanges_for_config_for_display(
        self, config_type: Literal["spot", "perpetual"]
    ) -> list[str]:

        if config_type == "spot":
            config = self.spot_config
        elif config_type == "perpetual":
            config = self.funding_rate_config
        else:
            raise ValueError(f"Invalid config type: {config_type}")

        return [ex.replace("v2lyra", "derive") for ex in config.keys()]


def get_bot_config(bot_id: str) -> dict[str, Any]:
    """
    Get the configuration for a specific bot.

    Args:
        bot_id: The ID of the bot to get configuration for

    Returns:
        A dictionary containing the bot configuration
    """

    global_supported_currencies: list[AllowedCurrencies] = (
        utils_general.json_loads(
            os.environ.get(
                "GLOBAL_SUPPORTED_VOL_CURRENCIES",
                ["BTC", "ETH", "ARB", "OP", "SUI"],
            )
        )
    )
    exchange_mapping = get_currency_exchange_mapping().root
    # todo: remove this when blockscholes/composite/v2composite SUI if redirected to blockscholes-syn
    if "SUI" in exchange_mapping:
        exchange_mapping["SUI"] = "blockscholes-syn"

    all_supported_currencies = set(
        get_all_supported_currencies(exchange_mapping)
    ).intersection(global_supported_currencies)

    configs = {
        BotNames.BLOCKSCHOLES.value: {
            "pricing_exchange": DEFAULT_EXCHANGE,
            "vol_charting_exchange": DEFAULT_EXCHANGE,
            "currency_to_exchange_mapping": exchange_mapping,
            "default_exchange": DEFAULT_EXCHANGE,
            "supported_vol_chart_currencies": all_supported_currencies,
            "supported_pricing_currencies": all_supported_currencies,
            "is_premium_bot": False,
        },
        BotNames.OKX.value: {
            "pricing_exchange": "okx",
            "vol_charting_exchange": "okx",
            "currency_to_exchange_mapping": {},
            "default_exchange": "okx",
            "supported_vol_chart_currencies": (
                list(
                    set(OKX_SUPPORTED_VOL_CHART_CURRENCIES).intersection(
                        global_supported_currencies
                    )
                )
            ),
            "supported_pricing_currencies": (
                list(
                    set(OKX_SUPPORTED_PRICE_CURRENCIES).intersection(
                        global_supported_currencies
                    )
                )
            ),
            "is_premium_bot": False,
        },
        BotNames.PREMIUM.value: {
            "pricing_exchange": DEFAULT_EXCHANGE,
            "vol_charting_exchange": DEFAULT_EXCHANGE,
            "currency_to_exchange_mapping": exchange_mapping,
            "default_exchange": DEFAULT_EXCHANGE,
            "supported_vol_chart_currencies": all_supported_currencies,
            "supported_pricing_currencies": all_supported_currencies,
            "is_premium_bot": True,
        },
        BotNames.LOCAL1.value: {  # t.me/blockscholes_testbot
            "pricing_exchange": DEFAULT_EXCHANGE,
            "vol_charting_exchange": DEFAULT_EXCHANGE,
            "currency_to_exchange_mapping": exchange_mapping,
            "default_exchange": DEFAULT_EXCHANGE,
            "supported_vol_chart_currencies": all_supported_currencies,
            "supported_pricing_currencies": all_supported_currencies,
            "is_premium_bot": False,
        },
        BotNames.LOCAL2.value: {  # t.me/BlockScholesLocal1Bot
            "pricing_exchange": "okx",
            "vol_charting_exchange": "okx",
            "currency_to_exchange_mapping": exchange_mapping,
            "default_exchange": "okx",
            "supported_vol_chart_currencies": (
                list(
                    set(OKX_SUPPORTED_PRICE_CURRENCIES).intersection(
                        global_supported_currencies
                    )
                )
            ),
            "supported_pricing_currencies": (
                list(
                    set(OKX_SUPPORTED_PRICE_CURRENCIES).intersection(
                        global_supported_currencies
                    )
                )
            ),
            "is_premium_bot": False,
        },
    }
    return configs[bot_id]
