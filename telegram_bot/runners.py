import functools
from typing import Any

from telegram import Update
from telegram.ext import (
    <PERSON><PERSON><PERSON><PERSON>,
    Command<PERSON><PERSON>ler,
    MessageHandler,
    TypeHandler,
    filters,
)

from telegram_bot.admin_utils import handle_unrecognized_command
from telegram_bot.configs.bot_config import <PERSON><PERSON><PERSON><PERSON>, BotNames
from telegram_bot.constants import PERMISSIONED_SIGNAL_CHAT_IDS
from telegram_bot.handlers.aux_handlers import (
    handle_contact_command,
    handle_help_command,
    handle_signup_command,
    handle_start_command,
)
from telegram_bot.handlers.callbacks import (
    capture_user_request_and_id_callback,
    rate_limit_callback,
)
from telegram_bot.handlers.charting import (
    handle_chart_request,
)
from telegram_bot.handlers.pricing import handle_price_request
from telegram_bot.handlers.signal import handle_signal_request
from telegram_bot.handlers.vol_run import handle_run_request
from telegram_bot.logger import <PERSON><PERSON><PERSON><PERSON><PERSON>
from telegram_bot.scheduled_jobs.wing_spread_signal import (
    wing_spread_scheduled_job,
)
from telegram_bot.typings import <PERSON><PERSON>, Stage


class BotBuilder:
    def __init__(
        self,
        bot_id: str,
        bot_api_key: str,
        config: dict[str, Any],
        aws_stage: Stage,
    ) -> None:
        """
        Initialize the BotBuilder with bot-specific configurations.

        :param bot_id: Unique identifier for the bot.
        :param bot_api_key: Telegram API key for the bot.
        :param config: Dictionary containing additional bot-specific configurations.
        """

        self.bot_id = bot_id
        self.bot_api_key = bot_api_key
        self._is_running = False

        self.logger = BotLogger(self.bot_id).get_logger()
        self.config = Botfig(
            whitelisted_users=config["whitelisted_users"],
            whitelisted_chats=config["whitelisted_chats"],
            supported_vol_chart_currencies=config[
                "supported_vol_chart_currencies"
            ],
            currency_to_exchange_mapping=config["currency_to_exchange_mapping"],
            supported_pricing_currencies=config["supported_pricing_currencies"],
            is_premium_bot=config["is_premium_bot"],
            internal_api_key=config["internal_api_key"],
            timeseries_version=config["timeseries_version"],
            funding_rate_config=config["funding_rate_config"],
            spot_config=config["spot_config"],
        )

        self.app = self._create_bot(aws_stage=aws_stage)
        if (
            bot_id == BotNames.BLOCKSCHOLES.value
            or bot_id == BotNames.LOCAL1.value
        ):
            for chat_id in PERMISSIONED_SIGNAL_CHAT_IDS:
                self._schedule_jobs(self.app, chat_id)
                self.logger.info(f"Adding chat_id {chat_id} to signal chats")

        self.logger.info("Bot initialized")

    def _schedule_jobs(self, app: Bot, chat_id: int) -> None:
        """
        Register periodic jobs on the JobQueue. These will begin running
        after `Application.start()` (which you already call in run_polling()).
        """

        jq = app.job_queue
        job_name = f"{self.bot_id}-hourly-:30"

        assert jq is not None
        jq.run_custom(
            callback=functools.partial(
                wing_spread_scheduled_job,
                logger=self.logger,
                botfig=self.config,
            ),
            job_kwargs={
                # forwarded to APScheduler's add_job(...)
                "trigger": "cron",
                "minute": 30,  # HH:15 every hour
                "second": 5,
                # "timezone": LONDON,  # wall-clock in London (DST-safe)
                "id": job_name,  # stable id avoids dupes
                "replace_existing": True,
                "coalesce": True,  #
                "misfire_grace_time": 5 * 60,  # tolerate short ECS restarts
            },
            name=job_name,
            # IMPORTANT NOTE: passing the chat_id to the callback means that we will have access to chat specific data available
            # in the bots context. This context is persisted thoughout the lifecycle of the bot. We use this to
            # manage chat specific state.
            chat_id=chat_id,
        )

    def _create_bot(self, aws_stage: Stage) -> Bot:
        """
        Create and configure the Telegram bot Application instance.
        """
        self.logger.info("Building bot")
        app = (
            ApplicationBuilder()
            .token(self.bot_api_key)
            .concurrent_updates(True)
            .build()
        )
        app = self._add_handlers(
            app=app,
            aws_stage=aws_stage,
        )
        return app

    def _add_handlers(self, app: Bot, aws_stage: Stage) -> Bot:
        """
        Add command and message handlers to the application. TypeHandlers with a lower group are prioritized
        relative to other handlers
        """
        self.logger.info("Adding handlers")

        # 1.) Add handler to capture user-id on every update
        try:
            user_id_and_request_capture_handler = TypeHandler(
                Update,
                functools.partial(
                    capture_user_request_and_id_callback,
                    logger=self.logger,
                    bot_id=self.bot_id,
                ),
            )
            app.add_handler(user_id_and_request_capture_handler, group=-2)
        except Exception as _e:
            self.logger.exception("Error adding capture user-id handler")
            raise

        # 2.) Add handler to rate-limit users
        try:
            rate_limit_handler = TypeHandler(
                Update,
                functools.partial(rate_limit_callback, logger=self.logger),
            )
            app.add_handler(rate_limit_handler, group=-1)
        except Exception as _e:
            self.logger.exception("Error while add rate-limiter")
            raise

        # 3.) Add a handler for the price command
        try:
            app.add_handler(
                CommandHandler(
                    "price",
                    functools.partial(
                        handle_price_request,
                        botfig=self.config,
                        aws_stage=aws_stage,
                        logger=self.logger,
                    ),
                )
            )
        except Exception as _e:
            self.logger.exception("Error while add '/price' command handler")
            raise

        # 4.) Add a handler for the chart command
        try:
            app.add_handler(
                CommandHandler(
                    "chart",
                    functools.partial(
                        handle_chart_request,
                        botfig=self.config,
                        logger=self.logger,
                    ),
                )
            )
        except Exception as _er:
            self.logger.exception("Error while add '/chart' command handler")
            raise

        # 5.) Add a handler for the run command
        try:
            app.add_handler(
                CommandHandler(
                    "run",
                    functools.partial(
                        handle_run_request,
                        botfig=self.config,
                        logger=self.logger,
                    ),
                )
            )
        except Exception as _er:
            self.logger.exception("Error while add '/run' command handler")
            raise

        # 6.) Add a handler for the signal command
        try:
            app.add_handler(
                CommandHandler(
                    "signal",
                    functools.partial(
                        handle_signal_request,
                        botfig=self.config,
                        logger=self.logger,
                    ),
                )
            )
        except Exception as _er:
            self.logger.exception("Error while add '/signal' command handler")
            raise

        # 7.) Add a handler for the /start command
        try:
            app.add_handler(
                CommandHandler(
                    "start",
                    functools.partial(
                        handle_start_command,
                        botfig=self.config,
                        logger=self.logger,
                    ),
                )
            )
        except Exception as _er:
            self.logger.exception("Error while add '/start' command handler")

        # 8.) Add a handler for the /signup command
        try:
            app.add_handler(
                CommandHandler(
                    "signup",
                    functools.partial(
                        handle_signup_command,
                        logger=self.logger,
                    ),
                )
            )
        except Exception as _er:
            self.logger.exception("Error while add '/signup' command handler")

        # 9.) Add a handler for the /contact command
        try:
            app.add_handler(
                CommandHandler(
                    "contact",
                    functools.partial(
                        handle_contact_command,
                        logger=self.logger,
                    ),
                )
            )
        except Exception as _er:
            self.logger.exception("Error while add '/contact' command handler")

        # 10.) Add a handler for the /help command
        try:
            app.add_handler(
                CommandHandler(
                    "help",
                    functools.partial(
                        handle_help_command,
                        botfig=self.config,
                        logger=self.logger,
                    ),
                )
            )
        except Exception as _er:
            self.logger.exception("Error while add '/help' command handler")
            raise

        # 11.) Add a handler unrecognised commands
        try:
            app.add_handler(
                MessageHandler(
                    filters.Regex(r"^/"),
                    functools.partial(
                        handle_unrecognized_command,
                        logger=self.logger,
                    ),
                )
            )
        except Exception as _er:
            self.logger.exception(
                "Error while adding handler for unsupported commands"
            )
            raise

        return app

    async def run_polling(self) -> None:
        """
        Run the bot using polling.
        """

        if self._is_running:
            self.logger.warning("Cannot start polling. Bot is already running")
            return

        self.logger.info("Starting polling.")
        try:
            await self.app.initialize()
            await self.app.start()

            self._is_running = True

            # Application builder sets a default updater
            if self.app.updater is not None:
                await self.app.updater.start_polling(drop_pending_updates=True)
            else:
                await self.shutdown()
                raise NotImplementedError("Updater is not set")

        except Exception as e:
            self.logger.error(f"Error while running polling: {e}")
            raise

    async def shutdown(self) -> None:
        """
        Gracefully shutdown the bot.
        """
        if self._is_running:
            self.logger.error("Failed to shutdown. Bot is not running")
            return

        self.logger.info("Stopping bot.")

        try:
            if self.app.updater is not None:
                await self.app.updater.stop()
            else:

                await self.app.stop()
                await self.app.shutdown()

                raise NotImplementedError("Updater is not set")

            await self.app.stop()
            await self.app.shutdown()

        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
