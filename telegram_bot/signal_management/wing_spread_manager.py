"""
Object-oriented wing spread signal management system.

This module provides classes for managing wing spread signals on a per-client basis,
including signal state tracking, threshold management, and client-specific configurations.
"""

import logging
from dataclasses import dataclass, field
from enum import Enum

from telegram_bot.constants import (
    DEFAULT_WING_SPREAD_TARGETS,
)
from telegram_bot.tg_bot_utils import (
    construct_wing_spread_key,
    get_wing_spread_threshold,
)
from telegram_bot.typings import ChartTimeseriesData, WingSpreadKey


class SignalState(Enum):
    """Enum representing the state of a signal relative to its threshold."""

    UNINITIALIZED = "uninitialized"
    BELOW_THRESHOLD = "below_threshold"
    ABOVE_THRESHOLD = "above_threshold"


@dataclass
class SignalCrossing:
    """Represents a signal crossing event."""

    signal_key: WingSpread<PERSON>ey
    crossed_above: bool
    value: float
    threshold: float
    previous_state: SignalState
    new_state: SignalState


@dataclass
class ClientSignalConfig:
    """Configuration for a specific client's signal preferences."""

    chat_id: int
    enabled_signals: set[WingSpreadKey] = field(default_factory=set)
    custom_thresholds: dict[WingSpreadKey, float] = field(default_factory=dict)

    def get_threshold(self, wing_spread_key: WingSpreadKey) -> float | None:
        """Get threshold for a signal, checking custom thresholds first."""
        currency, delta_key, tenor = wing_spread_key
        # Check custom threshold first
        if wing_spread_key in self.custom_thresholds:
            return self.custom_thresholds[wing_spread_key]

        # Fall back to default threshold
        return get_wing_spread_threshold(currency, delta_key, tenor)

    def is_signal_enabled(
        self, currency: str, delta_key: str, tenor: str
    ) -> bool:
        """Check if a signal is enabled for this client."""
        wing_spread_key = (currency, delta_key, tenor)

        # If no specific signals are configured, use default behavior
        if not self.enabled_signals:
            return (
                get_wing_spread_threshold(currency, delta_key, tenor)
                is not None
            )

        return wing_spread_key in self.enabled_signals


@dataclass
class ClientSignalState:
    """Tracks signal states for a specific client."""

    chat_id: int
    config: ClientSignalConfig
    signal_flags: dict[WingSpreadKey, SignalState] = field(default_factory=dict)

    def initialize_signal_state(
        self,
        signal_key: WingSpreadKey,
        is_above_threshold: bool,
        logger: logging.Logger,
    ) -> None:
        """
        Initialize a signal state without triggering crossing events.

        Args:
            signal_key: The signal identifier tuple (currency, delta_key, tenor)
            is_above_threshold: Whether the current value is above threshold
        """
        # Validate signal_key format
        if not isinstance(signal_key, tuple) or len(signal_key) != 3:
            logger.warning(
                f"Invalid signal key format during initialization: {signal_key}"
            )
            return

        initial_state = (
            SignalState.ABOVE_THRESHOLD
            if is_above_threshold
            else SignalState.BELOW_THRESHOLD
        )
        self.signal_flags[signal_key] = initial_state

        logger.info(
            f"Initialized signal {signal_key} to state {initial_state.value}"
        )

    def _update_signal_state(
        self,
        signal_key: WingSpreadKey,
        is_above_threshold: bool,
        logger: logging.Logger,
        value: float,
        threshold: float,
    ) -> SignalCrossing | None:
        """
        Update signal state and return crossing event if one occurred.

        Args:
            signal_key: The signal identifier tuple (currency, delta_key, tenor)
            is_above_threshold: Whether the current value is above threshold

        Returns:
            SignalCrossing if a crossing occurred, None otherwise
        """

        if signal_key not in self.signal_flags:
            logger.warning(
                f"Invalid signal key format during update: {signal_key}"
            )
            return None

        new_state = (
            SignalState.ABOVE_THRESHOLD
            if is_above_threshold
            else SignalState.BELOW_THRESHOLD
        )
        previous_state = self.signal_flags[signal_key]

        self.signal_flags[signal_key] = new_state
        # Check for crossing - only trigger when transitioning between ABOVE/BELOW states
        if self._is_crossing_event(previous_state, new_state):
            return SignalCrossing(
                signal_key=signal_key,
                crossed_above=is_above_threshold,
                value=value,
                threshold=threshold,
                previous_state=previous_state,
                new_state=new_state,
            )

        return None

    def _is_crossing_event(
        self, previous_state: SignalState, new_state: SignalState
    ) -> bool:
        """
        Determine if a state transition represents a crossing event.

        Args:
            previous_state: The previous signal state
            new_state: The new signal state

        Returns:
            True if this represents a threshold crossing, False otherwise
        """
        # Only consider transitions between ABOVE and BELOW as crossings
        crossing_transitions = {
            (SignalState.BELOW_THRESHOLD, SignalState.ABOVE_THRESHOLD),
            (SignalState.ABOVE_THRESHOLD, SignalState.BELOW_THRESHOLD),
        }
        return (previous_state, new_state) in crossing_transitions


class WingSpreadSignalManager:
    """Manages wing spread signals for multiple clients. The assumption for this is that it enaled one bot to store
    signals for multiple clients. Another framework that can be adopted is to have a singleton instance of this class
    per bot, and associate each instance with a client/chat
    """

    def __init__(
        self,
        logger: logging.Logger,
        chat_id: int,
        latest_values: dict[WingSpreadKey, float],
    ):
        self.logger = logger
        self._initialize_default_clients(
            chat_id=chat_id, latest_values=latest_values
        )

    def _initialize_default_clients(
        self,
        chat_id: int,
        latest_values: dict[WingSpreadKey, float],
    ) -> None:
        """Initialize client states for all clinet wing spread signals."""
        self.client_state = ClientSignalState(
            chat_id=chat_id,
            config=create_default_client_config(chat_id=chat_id),
        )
        client_config = self.client_state.config

        # Currently we don't have a way to determine the initial state of the signal. We require some sort of connection
        # to an external db to be able to determine the initial state. This will happen everytimes the TG bot is
        # restarted and looses context of the signal states
        # As a workaround, we initialize the signal state to whatever the current state is. The assumption here is that
        # if there was a state transition in the previous hour, we would have sent a notification. It also ensures that
        # we capture ALL subsequent state.
        # The unhandled edge case here happens if the hour in which the bot restarts in, has state transitions. We would
        # be assuming that the previous hour was in the same state as the current hour.
        for wing_spread_key in client_config.enabled_signals:

            threshold = client_config.get_threshold(wing_spread_key)
            assert threshold is not None
            if wing_spread_key not in latest_values:
                continue
            is_above_threshold = latest_values[wing_spread_key] >= threshold
            self.client_state.initialize_signal_state(
                signal_key=wing_spread_key,
                is_above_threshold=is_above_threshold,
                logger=self.logger,
            )

    def process_signal_data(
        self,
        latest_values: dict[WingSpreadKey, float],
        logger: logging.Logger,
    ) -> list[SignalCrossing]:
        """
        Process signal data for all clients and return crossing events.

        Args:
            latest_values: Dictionary mapping signal keys to their latest values

        Returns:
            Dictionary mapping chat_id to list of crossing events for that client
        """

        client_crossings: list[SignalCrossing] = []

        for wing_spread_key, value in latest_values.items():
            try:
                currency, delta_key, tenor = wing_spread_key
            except ValueError:
                # Skip invalid signal key format
                continue

            # Check if this signal is enabled for this client
            if not self.client_state.config.is_signal_enabled(
                currency, delta_key, tenor
            ):
                continue

            # Get threshold for this client
            threshold = self.client_state.config.get_threshold(wing_spread_key)
            if threshold is None:
                continue

            # Check if value crosses threshold
            is_above_threshold = value >= threshold
            crossing = self.client_state._update_signal_state(
                signal_key=wing_spread_key,
                is_above_threshold=is_above_threshold,
                logger=logger,
                value=value,
                threshold=threshold,
            )

            if crossing is not None:
                client_crossings.append(crossing)

        return client_crossings


def extract_latest_values(
    data: ChartTimeseriesData,
) -> dict[WingSpreadKey, float]:
    """
    Extract the latest values from chart timeseries data.

    Args:
        data: Chart timeseries data

    Returns:
        Dictionary mapping signal keys to their latest values
    """
    latest: dict[WingSpreadKey, float] = {}
    for key, series in data.items():
        currency, delta_key, tenor = key.split("_")
        wing_spread_key = construct_wing_spread_key(
            currency=currency,
            delta_key=delta_key,
            tenor=tenor,
        )
        if not series:
            continue
        try:
            newest = max(series, key=lambda x: x.get("timestamp", 0) or 0)
            latest[wing_spread_key] = float(newest["value"])
        except Exception:
            continue
    return latest


def create_default_client_config(chat_id: int) -> ClientSignalConfig:
    """
    Create a default client configuration with all available signals enabled.

    Args:
        chat_id: The client's chat ID

    Returns:
        ClientSignalConfig with default settings
    """
    config = ClientSignalConfig(chat_id=chat_id)

    # Enable all signals from DEFAULT_WING_SPREAD_TARGETS
    config.enabled_signals = set(DEFAULT_WING_SPREAD_TARGETS.keys())

    return config
