import logging
from typing import Any, Final

from telegram.constants import ParseMode
from telegram.ext import ContextTypes

from telegram_bot.configs.bot_config import Botfig
from telegram_bot.handlers.chart_builder import (
    build_wing_spread_signal_plot_objects,
)
from telegram_bot.handlers.signal import (
    get_signal_charts_and_data,
    send_signal_charts_and_data,
)
from telegram_bot.logger import current_chat_id
from telegram_bot.signal_management.wing_spread_manager import (
    SignalCrossing,
    WingSpreadSignalManager,
    extract_latest_values,
)
from telegram_bot.typings import (
    ChartsAndData,
    DecodedLambdaPayload,
    ValidatedSignalChartRequest,
    WingSpreadKey,
)

_MANAGER_KEY: Final[str] = "wing_spread_manager"


def _build_crossing_message(crossings: list[SignalCrossing]) -> str:
    """Build a message describing signal crossings."""
    lines: list[str] = ["Wing Spread Signal Update"]

    crossed_above = [c for c in crossings if c.crossed_above]
    crossed_below = [c for c in crossings if not c.crossed_above]

    if crossed_above:
        lines.append("\nCrossed ABOVE threshold:")
        for crossing in crossed_above:
            currency, delta_key, tenor = crossing.signal_key
            lines.append(
                f" - {currency} {delta_key} {tenor} "
                f"(Value: {crossing.value:.2f}, Threshold: {crossing.threshold:.2f})"
            )

    if crossed_below:
        lines.append("\nDropped BELOW threshold:")
        for crossing in crossed_below:
            currency, delta_key, tenor = crossing.signal_key
            lines.append(
                f" - {currency} {delta_key} {tenor} "
                f"(Value: {crossing.value:.2f}, Threshold: {crossing.threshold:.2f})"
            )

    return "\n".join(lines)


async def _send_signal_to_client(
    context: ContextTypes.DEFAULT_TYPE,
    chat_id: int,
    image: Any,
    caption: str,
    logger: logging.Logger,
) -> None:
    """Send signal chart to a specific client."""
    try:
        await context.bot.send_photo(
            chat_id=chat_id, photo=image, caption=caption
        )
        logger.info(f"Sent wing spread signal chart to client {chat_id}")
    except Exception as e:
        logger.error(
            f"Failed to send wing spread signal chart to client {chat_id}: {e}"
        )


def _get_or_create_manager(
    chat_data: dict[str, Any],
    logger: logging.Logger,
    chat_id: int,
    latest_values: dict[WingSpreadKey, float],
) -> WingSpreadSignalManager:
    """Get or create the wing spread signal manager from bot data."""
    wing_spread_manager = chat_data.get(_MANAGER_KEY)
    if wing_spread_manager is None:
        wing_spread_manager = WingSpreadSignalManager(
            logger=logger, chat_id=chat_id, latest_values=latest_values
        )
        # Load existing state for backward compatibility
        chat_data[_MANAGER_KEY] = wing_spread_manager
        logger.info("Created new WingSpreadSignalManager")

    return wing_spread_manager


async def wing_spread_scheduled_job(
    context: ContextTypes.DEFAULT_TYPE,
    logger: logging.Logger,
    botfig: Botfig,
) -> None:
    """Process scheduled wing spread signals using the WingSpreadSignalManager."""

    # will fail is function if called outside the job context
    assert context.job is not None
    assert context.job.chat_id is not None
    # IMPORTANT NOTE. wing_spread_scheduled_job is currently used as a custom job on a bot. We pass in the chat_id to
    # this custom job which creates a chat_data object that is available throughout the lifecycle of bot and stores
    # ANY chat-specific data. We use this to store the signal manager for each chat.
    assert context.chat_data is not None
    chat_id = context.job.chat_id
    chat_data: dict[str, Any] = context.chat_data
    current_chat_id.set(chat_id)

    signal_request = ValidatedSignalChartRequest(chart_type="wing spread")

    try:
        plot_objects = await build_wing_spread_signal_plot_objects()

        decoded_images_and_data: ChartsAndData = (
            await get_signal_charts_and_data(
                signal_request=signal_request,
                plot_objects=plot_objects,
                botfig=botfig,
                logger=logger,
            )
        )
    except Exception:
        logger.exception(
            "Failed to build wing spread scheduled signal charts/data"
        )
        return

    # Expect a single chart under v2timeseries
    payloads = decoded_images_and_data.get("v2timeseries", {})
    if not payloads:
        logger.error("No timeseries payloads found for wing spread signals")
        return

    # Pick the first (and only) chart payload
    chart_name, payload = next(iter(payloads.items()))
    data = payload.get("data")
    if not data:
        logger.error(
            "No data returned for wing spread signals; nothing to evaluate"
        )
        return

    # Extract latest values from the data
    latest_values = extract_latest_values(data)

    wing_spread_manager = _get_or_create_manager(
        chat_data, logger, chat_id, latest_values
    )
    # Process signals for all clients
    all_crossings = wing_spread_manager.process_signal_data(
        latest_values=latest_values, logger=logger
    )

    if not all_crossings:
        logger.info(
            "No wing spread threshold crossings detected for any client; nothing to send"
        )
        return

    if all_crossings:
        # all_crossings custom caption with crossing information
        crossing_caption = _build_crossing_message(all_crossings)
        await context.bot.send_message(
            chat_id=chat_id,
            text=crossing_caption,
            parse_mode=ParseMode.HTML,
        )
        # Modify the chart data to include custom caption
        modified_data: ChartsAndData = {
            "v2timeseries": {
                chart_name: DecodedLambdaPayload(
                    image=payload["image"],
                    data=payload.get("data"),
                )
            }
        }

        await send_signal_charts_and_data(
            decoded_images_and_data=modified_data,
            context=context,
            chat_id=chat_id,
            logger=logger,
        )

    logger.info(
        f"Sent wing spread signal chart '{chart_name}' sent to {chat_id} "
        f"with {len(all_crossings)} total crossings"
    )
