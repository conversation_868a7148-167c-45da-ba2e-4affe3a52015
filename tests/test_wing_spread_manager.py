"""Tests for the wing spread signal management system."""

import logging
from unittest.mock import Mock

import pytest

from telegram_bot.signal_management.wing_spread_manager import (
    ClientSignalConfig,
    ClientSignalState,
    SignalCrossing,
    SignalState,
    WingSpreadSignalManager,
    create_default_client_config,
    extract_latest_values,
)
from telegram_bot.typings import ChartTimeseriesData


@pytest.fixture
def logger() -> Mock:
    """Create a mock logger for testing."""
    return Mock(spec=logging.Logger)


@pytest.fixture
def sample_timeseries_data() -> ChartTimeseriesData:
    """Create sample timeseries data for testing."""
    return {
        "BTC_5delta_30d": [
            {"timestamp": 1000, "value": 2.0},
            {"timestamp": 2000, "value": 3.0},  # Latest value
        ],
        "ETH_5delta_180d": [
            {"timestamp": 1000, "value": 1.5},
            {"timestamp": 2000, "value": 2.8},  # Latest value
        ],
        "ETH_-5delta_180d": [
            {"timestamp": 1000, "value": 3.0},
            {"timestamp": 2000, "value": 2.0},  # Latest value
        ],
    }


class TestExtractLatestValues:
    """Test the extract_latest_values function."""

    def test_extract_latest_values_success(
        self, sample_timeseries_data: ChartTimeseriesData
    ) -> None:
        """Test successful extraction of latest values."""
        result = extract_latest_values(sample_timeseries_data)

        expected = {
            ("BTC", "5delta", "30d"): 3.0,
            ("ETH", "5delta", "180d"): 2.8,
            ("ETH", "-5delta", "180d"): 2.0,
        }
        assert result == expected

    def test_extract_latest_values_empty_series(self) -> None:
        """Test handling of empty series."""
        data: ChartTimeseriesData = {
            "BTC_5delta_30d": [],
            "ETH_5delta_180d": [{"timestamp": 1000, "value": 2.5}],
        }
        result = extract_latest_values(data)

        expected = {("ETH", "5delta", "180d"): 2.5}
        assert result == expected


class TestClientSignalConfig:
    """Test the ClientSignalConfig class."""

    def test_get_threshold_custom(self) -> None:
        """Test getting custom threshold."""
        config = ClientSignalConfig(chat_id=123)
        config.custom_thresholds[("BTC", "5delta", "30d")] = 3.5

        threshold = config.get_threshold(("BTC", "5delta", "30d"))
        assert threshold == 3.5

    def test_get_threshold_default(self) -> None:
        """Test falling back to default threshold."""
        config = ClientSignalConfig(chat_id=123)

        # This should fall back to the default threshold lookup
        threshold = config.get_threshold(("BTC", "5delta", "30d"))
        assert threshold == 2.5  # DEFAULT_WING_SPREAD_SIGNAL_THRESHOLD

    def test_is_signal_enabled_with_specific_signals(self) -> None:
        """Test signal enablement with specific signals configured."""
        config = ClientSignalConfig(chat_id=123)
        config.enabled_signals.add(("BTC", "5delta", "30d"))

        assert config.is_signal_enabled("BTC", "5delta", "30d") is True
        assert config.is_signal_enabled("ETH", "5delta", "180d") is False

    def test_is_signal_enabled_default_behavior(self) -> None:
        """Test signal enablement with default behavior."""
        config = ClientSignalConfig(chat_id=123)
        # No specific signals configured, should use default behavior

        # These should return True if they have default thresholds
        assert config.is_signal_enabled("BTC", "5delta", "30d") is True
        assert config.is_signal_enabled("ETH", "5delta", "180d") is True


class TestClientSignalState:
    """Test the ClientSignalState class."""

    def test_update_signal_state_crossing_above(
        self, logger: logging.Logger
    ) -> None:
        """Test crossing above threshold."""
        config = ClientSignalConfig(chat_id=123)
        state = ClientSignalState(chat_id=123, config=config)

        # Initialize state
        signal_key = ("BTC", "5delta", "30d")
        state.signal_flags[signal_key] = SignalState.BELOW_THRESHOLD

        # Trigger crossing
        crossing = state._update_signal_state(
            signal_key=signal_key,
            is_above_threshold=True,
            logger=logger,
            value=3.0,
            threshold=2.5,
        )

        assert crossing is not None
        assert crossing.signal_key == signal_key
        assert crossing.crossed_above is True
        assert crossing.value == 3.0
        assert crossing.threshold == 2.5
        assert crossing.previous_state == SignalState.BELOW_THRESHOLD
        assert crossing.new_state == SignalState.ABOVE_THRESHOLD
        assert state.signal_flags[signal_key] == SignalState.ABOVE_THRESHOLD

    def test_update_signal_state_crossing_below(
        self, logger: logging.Logger
    ) -> None:
        """Test crossing below threshold."""
        config = ClientSignalConfig(chat_id=123)
        state = ClientSignalState(chat_id=123, config=config)

        # Initialize state
        signal_key = ("BTC", "5delta", "30d")
        state.signal_flags[signal_key] = SignalState.ABOVE_THRESHOLD

        # Trigger crossing
        crossing = state._update_signal_state(
            signal_key=signal_key,
            is_above_threshold=False,
            logger=logger,
            value=2.0,
            threshold=2.5,
        )

        assert crossing is not None
        assert crossing.crossed_above is False
        assert crossing.value == 2.0
        assert crossing.threshold == 2.5
        assert crossing.previous_state == SignalState.ABOVE_THRESHOLD
        assert crossing.new_state == SignalState.BELOW_THRESHOLD
        assert state.signal_flags[signal_key] == SignalState.BELOW_THRESHOLD

    def test_update_signal_state_no_crossing(
        self, logger: logging.Logger
    ) -> None:
        """Test no crossing when state doesn't change."""
        config = ClientSignalConfig(chat_id=123)
        state = ClientSignalState(chat_id=123, config=config)

        # Initialize state
        signal_key = ("BTC", "5delta", "30d")
        state.signal_flags[signal_key] = SignalState.ABOVE_THRESHOLD

        # No change
        crossing = state._update_signal_state(
            signal_key=signal_key,
            is_above_threshold=True,
            logger=logger,
            value=3.0,
            threshold=2.5,
        )

        assert crossing is None
        assert state.signal_flags[signal_key] == SignalState.ABOVE_THRESHOLD

    def test_initialize_signal_state(self, logger: logging.Logger) -> None:
        """Test signal state initialization."""
        config = ClientSignalConfig(chat_id=123)
        state = ClientSignalState(chat_id=123, config=config)

        signal_key = ("BTC", "5delta", "30d")
        state.initialize_signal_state(signal_key, True, logger)

        assert state.signal_flags[signal_key] == SignalState.ABOVE_THRESHOLD


class TestWingSpreadSignalManager:
    """Test the WingSpreadSignalManager class."""

    def test_initialization(self, logger: logging.Logger) -> None:
        """Test manager initialization."""
        chat_id = 123
        latest_values = {
            ("ETH", "-5delta", "180d"): 3.0,
            ("ETH", "5delta", "180d"): 2.0,
        }
        manager = WingSpreadSignalManager(logger, chat_id, latest_values)

        # Should have initialized client state for the given chat ID
        assert manager.client_state.chat_id == chat_id
        assert manager.client_state.config.chat_id == chat_id

    def test_process_signal_data(self, logger: logging.Logger) -> None:
        """Test processing signal data for a single client."""
        chat_id = 123
        initial_values = {
            ("BTC", "5delta", "30d"): 2.0,
            ("ETH", "5delta", "180d"): 3.0,
        }
        manager = WingSpreadSignalManager(logger, chat_id, initial_values)

        # Set up test data
        latest_values = {
            ("BTC", "5delta", "30d"): 3.0,  # Above threshold (2.5)
            ("ETH", "5delta", "180d"): 2.0,  # Below threshold (2.5)
        }

        # Initialize some previous states to trigger crossings
        manager.client_state.signal_flags[("BTC", "5delta", "30d")] = (
            SignalState.BELOW_THRESHOLD
        )  # Will cross above
        manager.client_state.signal_flags[("ETH", "5delta", "180d")] = (
            SignalState.ABOVE_THRESHOLD
        )  # Will cross below

        # Process the data
        crossings = manager.process_signal_data(latest_values, logger)

        # Should have 2 crossings
        assert len(crossings) == 2

        # Find the crossings
        btc_crossing = next(c for c in crossings if c.signal_key[0] == "BTC")
        eth_crossing = next(c for c in crossings if c.signal_key[0] == "ETH")

        assert btc_crossing.crossed_above is True
        assert btc_crossing.value == 3.0
        assert btc_crossing.threshold == 2.5
        assert eth_crossing.crossed_above is False
        assert eth_crossing.value == 2.0
        assert eth_crossing.threshold == 2.5

    def test_process_signal_data_no_crossings(
        self, logger: logging.Logger
    ) -> None:
        """Test processing signal data with no crossings."""
        chat_id = 123
        initial_values = {
            ("BTC", "5delta", "30d"): 3.0,
        }
        manager = WingSpreadSignalManager(logger, chat_id, initial_values)

        # Set up test data
        latest_values = {
            ("BTC", "5delta", "30d"): 3.0,  # Above threshold (2.5)
        }

        # Initialize state to same condition (no crossing)
        manager.client_state.signal_flags[("BTC", "5delta", "30d")] = (
            SignalState.ABOVE_THRESHOLD
        )

        # Process the data
        crossings = manager.process_signal_data(latest_values, logger)

        # Should have no crossings
        assert len(crossings) == 0

    def test_process_signal_data_disabled_signal(
        self, logger: logging.Logger
    ) -> None:
        """Test processing signal data with disabled signals."""
        chat_id = 123
        initial_values = {
            ("BTC", "5delta", "30d"): 2.0,
            ("ETH", "5delta", "180d"): 2.0,
        }
        manager = WingSpreadSignalManager(logger, chat_id, initial_values)

        # Disable all signals except one
        manager.client_state.config.enabled_signals = {("BTC", "5delta", "30d")}

        # Set up test data
        latest_values = {
            ("BTC", "5delta", "30d"): 3.0,  # Enabled signal
            ("ETH", "5delta", "180d"): 3.0,  # Disabled signal
        }

        # Initialize states to trigger crossings
        manager.client_state.signal_flags[("BTC", "5delta", "30d")] = (
            SignalState.BELOW_THRESHOLD
        )
        manager.client_state.signal_flags[("ETH", "5delta", "180d")] = (
            SignalState.BELOW_THRESHOLD
        )

        # Process the data
        crossings = manager.process_signal_data(latest_values, logger)

        # Should only have crossing for enabled signal
        assert len(crossings) == 1
        assert crossings[0].signal_key == ("BTC", "5delta", "30d")


class TestConfigurationFunctions:
    """Test configuration-related functions."""

    def test_create_default_client_config(self) -> None:
        """Test creating default client configuration."""
        config = create_default_client_config(123)

        assert config.chat_id == 123
        assert (
            len(config.enabled_signals) > 0
        )  # Should have some default signals
        assert (
            len(config.custom_thresholds) == 0
        )  # No custom thresholds by default


class TestSignalCrossing:
    """Test the SignalCrossing dataclass."""

    def test_signal_crossing_creation(self) -> None:
        """Test creating a SignalCrossing instance."""
        signal_key = ("BTC", "5delta", "30d")
        crossing = SignalCrossing(
            signal_key=signal_key,
            crossed_above=True,
            value=3.0,
            threshold=2.5,
            previous_state=SignalState.BELOW_THRESHOLD,
            new_state=SignalState.ABOVE_THRESHOLD,
        )

        assert crossing.signal_key == signal_key
        assert crossing.crossed_above is True
        assert crossing.value == 3.0
        assert crossing.threshold == 2.5
        assert crossing.previous_state == SignalState.BELOW_THRESHOLD
        assert crossing.new_state == SignalState.ABOVE_THRESHOLD


class TestSignalState:
    """Test the SignalState enum."""

    def test_signal_state_values(self) -> None:
        """Test SignalState enum values."""
        assert SignalState.UNINITIALIZED.value == "uninitialized"
        assert SignalState.BELOW_THRESHOLD.value == "below_threshold"
        assert SignalState.ABOVE_THRESHOLD.value == "above_threshold"
